package com.zsmall.activity.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.excel.convert.ExcelBigNumberConvert;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.StorageFeeInfo;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeeInfoBo;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeePayBo;
import com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeInfoVo;
import com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeItemVo;
import com.zsmall.activity.entity.mapper.StorageFeeInfoMapper;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.domain.CurrencyAmountVO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.storageFee.FeeStateEnum;
import com.zsmall.common.enums.storageFee.PayStateEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.event.CheckPaymentPasswordEvent;
import com.zsmall.system.entity.domain.vo.funds.DebitRecordVo;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓储费主Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class IStorageFeeInfoService extends ServiceImpl<StorageFeeInfoMapper, StorageFeeInfo> {

    private final IStorageFeeItemService iStorageFeeItemService;
    private final StorageFeeInfoMapper baseMapper;

    /**
     * 查询仓储费主
     */
    public StorageFeeInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询仓储费主列表
     */
    public TableDataInfo<StorageFeeInfoVo> queryPageList(StorageFeeInfoBo bo, PageQuery pageQuery) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        if(null !=  tenantTypeEnum && tenantTypeEnum.equals(TenantType.Distributor)){
            bo.setTenantId(LoginHelper.getTenantId());
            bo.setFeeStateList(Arrays.asList(FeeStateEnum.CONFIRMING.getValue(),FeeStateEnum.CONFIRMED.getValue()));
        }
        LambdaQueryWrapper<StorageFeeInfo> lqw = buildQueryWrapper(bo);
        Page<StorageFeeInfoVo> result;
        if(null !=  tenantTypeEnum && tenantTypeEnum.equals(TenantType.Manager)){
            // 管理员根据费用状态排序
            if(null != bo.getFeeState()){
                switch (FeeStateEnum.getByValue(bo.getFeeState())){
                    case CONFIRMED:
                        lqw.orderByDesc(StorageFeeInfo::getPayTime);
                        break;
                        case CONFIRMING:
                        lqw.orderByDesc(StorageFeeInfo::getSendTime);
                        break;
                        case WAIT_CONFIRM:
                        lqw.orderByDesc(StorageFeeInfo::getStorageFeeSettlementDate);
                        break;
                        default:
                        lqw.orderByDesc(StorageFeeInfo::getCreateTime);
                        break;
                }
            }
            result = TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw));
        }else {
            // 分销商根据支付状态排序
            if(null != bo.getPayState()){
                switch (PayStateEnum.getByValue(bo.getPayState())){
                    case UNPAID:
                        lqw.orderByDesc(StorageFeeInfo::getSendTime);
                        break;
                    case PAID:
                        lqw.orderByDesc(StorageFeeInfo::getPayTime);
                        break;
                    default:
                        lqw.orderByDesc(StorageFeeInfo::getCreateTime);
                        break;
                }
            }
            result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询仓储费主列表
     */
    public List<StorageFeeInfoVo> queryList(StorageFeeInfoBo bo) {
        LambdaQueryWrapper<StorageFeeInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StorageFeeInfo> buildQueryWrapper(StorageFeeInfoBo bo) {
        LambdaQueryWrapper<StorageFeeInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStorageFeeId()), StorageFeeInfo::getStorageFeeId, bo.getStorageFeeId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), StorageFeeInfo::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencyCode()), StorageFeeInfo::getCurrencyCode, bo.getCurrencyCode());
        lqw.eq(bo.getTotalStorageFee() != null, StorageFeeInfo::getTotalStorageFee, bo.getTotalStorageFee());
        lqw.eq(bo.getPayState() != null, StorageFeeInfo::getPayState, bo.getPayState());
        if(null != bo.getStorageFeeSettlementDateStart() && null != bo.getStorageFeeSettlementDateEnd()){
            lqw.between(StorageFeeInfo::getStorageFeeSettlementDate, bo.getStorageFeeSettlementDateStart(),bo.getStorageFeeSettlementDateEnd());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getActivityId()), StorageFeeInfo::getActivityId, bo.getActivityId());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityType()), StorageFeeInfo::getActivityType, bo.getActivityType());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsType()), StorageFeeInfo::getLogisticsType, bo.getLogisticsType());
        lqw.eq(bo.getFeeState() != null, StorageFeeInfo::getFeeState, bo.getFeeState());
        lqw.in(CollUtil.isNotEmpty(bo.getFeeStateList()), StorageFeeInfo::getFeeState, bo.getFeeStateList());
        if(null != bo.getSendTimeStart() && null != bo.getSendTimeEnd()){
            lqw.between(StorageFeeInfo::getSendTime, bo.getSendTimeStart(),bo.getSendTimeEnd());
        }
        if(null != bo.getPayTimeStart() && null != bo.getPayTimeEnd()){
            lqw.between(StorageFeeInfo::getPayTime, bo.getPayTimeStart(),bo.getPayTimeEnd());
        }
        // 导出id
        lqw.in(CollUtil.isNotEmpty(bo.getIds()), StorageFeeInfo::getId, bo.getIds());
        return lqw;
    }

    /**
     * 新增仓储费主
     */
    public Boolean insertByBo(StorageFeeInfoBo bo) {
        StorageFeeInfo add = MapstructUtils.convert(bo, StorageFeeInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改仓储费主
     */
    public Boolean updateByBo(StorageFeeInfoBo bo) {
        StorageFeeInfo update = MapstructUtils.convert(bo, StorageFeeInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StorageFeeInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除仓储费主
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("发送仓储费到分销商方法")
    public void sendStorageFee(List<String> storageFeeIdList){
        LambdaUpdateWrapper<StorageFeeInfo> lambdaUpdate = Wrappers.lambdaUpdate();
        lambdaUpdate.set(StorageFeeInfo::getFeeState, FeeStateEnum.CONFIRMING.getValue());
        lambdaUpdate.in(StorageFeeInfo::getStorageFeeId, storageFeeIdList);
        baseMapper.update(null, lambdaUpdate);
    }

    @InMethodLog("仓储费excel导出方法")
    public void exportStorageFee(StorageFeeInfoBo bo, HttpServletResponse response) {
        LambdaQueryWrapper<StorageFeeInfo> lqw = buildQueryWrapper(bo);
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        if(null !=  tenantTypeEnum && tenantTypeEnum.equals(TenantType.Distributor)){
            bo.setTenantId(LoginHelper.getTenantId());
            bo.setFeeStateList(Arrays.asList(FeeStateEnum.CONFIRMING.getValue(),FeeStateEnum.CONFIRMED.getValue()));
        }
        if(null !=  tenantTypeEnum && tenantTypeEnum.equals(TenantType.Manager)){
            // 管理员根据费用状态排序
            if(null != bo.getFeeState()){
                switch (FeeStateEnum.getByValue(bo.getFeeState())){
                    case CONFIRMED:
                        lqw.orderByDesc(StorageFeeInfo::getPayTime);
                        break;
                    case CONFIRMING:
                        lqw.orderByDesc(StorageFeeInfo::getSendTime);
                        break;
                    case WAIT_CONFIRM:
                        lqw.orderByDesc(StorageFeeInfo::getStorageFeeSettlementDate);
                        break;
                    default:
                        lqw.orderByDesc(StorageFeeInfo::getCreateTime);
                        break;
                }
            }
        }else {
            // 分销商根据支付状态排序
            if(null != bo.getPayState()){
                switch (PayStateEnum.getByValue(bo.getFeeState())){
                    case UNPAID:
                        lqw.orderByDesc(StorageFeeInfo::getSendTime);
                        break;
                    case PAID:
                        lqw.orderByDesc(StorageFeeInfo::getPayTime);
                        break;
                    default:
                        lqw.orderByDesc(StorageFeeInfo::getCreateTime);
                        break;
                }
            }
        }
        List<StorageFeeInfoVo> storageFeeInfoVoList = baseMapper.selectVoList(lqw);
        if(CollUtil.isNotEmpty(storageFeeInfoVoList)){
            for (StorageFeeInfoVo storageFeeInfoVo : storageFeeInfoVoList){
                if(StringUtils.isNotBlank(storageFeeInfoVo.getCurrencySymbol()) && null != storageFeeInfoVo.getTotalStorageFee()){
                    storageFeeInfoVo.setTotalStorageFeeString(storageFeeInfoVo.getCurrencySymbol() + storageFeeInfoVo.getTotalStorageFee());
                }
                if(null != storageFeeInfoVo.getFeeState()){
                    storageFeeInfoVo.setFeeStateString(FeeStateEnum.getByValue(storageFeeInfoVo.getFeeState()).getName());
                }
                if(null != storageFeeInfoVo.getPayState()){
                    storageFeeInfoVo.setPayStateString(PayStateEnum.getByValue(storageFeeInfoVo.getPayState()).getName());
                }
                if(null != storageFeeInfoVo.getActivityType()){
                    storageFeeInfoVo.setActivityType(ProductActivityTypeEnum.getNameByCode(storageFeeInfoVo.getActivityType()));
                }
            }
        }
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.STORAGE_FEE_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName,DownloadTypePlusEnum.StorageFeeExport, tempFileSavePath -> {
                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                try {
                    ExcelUtil.resetResponse("分销商账单导出", response, false);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
                ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
                // 仓储费汇总数据
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "仓储费汇总数据")
                                                 // 这里放入动态头
                                                 .head(StorageFeeInfoVo.class)
                                                 //传入样式
                                                 .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                                 .registerConverter(new ExcelBigNumberConvert())
                                                 .build();
                // 设置国际化表头
                writeSheet.setHead(ExcelUtil.getLocaleHeaders(StorageFeeInfoVo.class, headerLocale));
                excelWriter.write(storageFeeInfoVoList, writeSheet);
                // 仓储费明细数据
                WriteSheet writeSheetDetail = EasyExcel.writerSheet(1, "仓储费明细数据")
                                                 .head(StorageFeeItemVo.class)
                                                 .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                                 .registerConverter(new ExcelBigNumberConvert())
                                                 .build();
                List<StorageFeeItemVo> storageFeeItemVoList = iStorageFeeItemService.queryExportList(storageFeeInfoVoList.stream()
                                                                                                                  .map(StorageFeeInfoVo::getStorageFeeId)
                                                                                                                  .collect(Collectors.toList()));
                if(CollUtil.isNotEmpty(storageFeeItemVoList)){
                    for (StorageFeeItemVo storageFeeItemVo : storageFeeItemVoList){
                        if(StringUtils.isNotBlank(storageFeeItemVo.getCurrencyCode())){
                            if(null != storageFeeItemVo.getStorageFee()){
                                storageFeeItemVo.setStorageFeeString(storageFeeItemVo.getCurrencySymbol() + storageFeeItemVo.getStorageFee());
                            }
                            if(null != storageFeeItemVo.getStorageFeeDay()){
                                storageFeeItemVo.setStorageFeeDayString(storageFeeItemVo.getCurrencySymbol() + storageFeeItemVo.getStorageFeeDay());
                            }
                            if(StringUtils.isNotBlank(storageFeeItemVo.getSite())){
                                storageFeeItemVo.setSiteString(storageFeeItemVo.getSite() +"/"+ storageFeeItemVo.getCurrencySymbol());
                            }
                        }
                    }
                }
                // 设置国际化表头
                writeSheetDetail.setHead(ExcelUtil.getLocaleHeaders(StorageFeeItemVo.class, headerLocale));
                excelWriter.write(storageFeeItemVoList, writeSheetDetail);
                excelWriter.finish();
                IoUtil.close(outputStream);
                return tempFile;
        });
    }

    @InMethodLog("获取仓储费金额")
    public R<List<CurrencyAmountVO>> getStorageFee(List<String> storageFeeIds){
        // 判断仓储费的状态是否为确认中
        List<StorageFeeInfo> storageFeeInfoList = this.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, storageFeeIds));
        if(CollUtil.isEmpty(storageFeeInfoList)){
            log.info("仓储费不存在");
            return null;
        }
        // 错误状态的仓储费ID
        List<String> errorStorageFeeIdList = new ArrayList<>();
        for (StorageFeeInfo storageFeeInfo : storageFeeInfoList){
            if(!FeeStateEnum.CONFIRMING.getValue().equals(storageFeeInfo.getFeeState())){
                errorStorageFeeIdList.add(storageFeeInfo.getStorageFeeId());
            }
        }
        if(CollUtil.isNotEmpty(errorStorageFeeIdList)){
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_STATE_ERROR.args(errorStorageFeeIdList));
        }
        List<CurrencyAmountVO> currencyAmountVOList = storageFeeInfoList.stream()
                                                                        .collect(Collectors.groupingBy(StorageFeeInfo::getCurrencyCode))
                                                                        .entrySet()
                                                                        .stream()
                                                                        .map(entry -> {
                                                                            List<StorageFeeInfo> groupList = entry.getValue();
                                                                            // 计算总金额
                                                                            BigDecimal total = groupList.stream()
                                                                                                        .map(StorageFeeInfo::getTotalStorageFee)
                                                                                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                                                            // 获取currencySymbol（取第一个元素的值，假设同组currencySymbol一致）
                                                                            String currencySymbol = groupList.get(0).getCurrencySymbol();
                                                                            // 构建CurrencyAmountVO对象
                                                                            return CurrencyAmountVO.builder()
                                                                                                   .currency(entry.getKey())
                                                                                                   .currencySymbol(currencySymbol)
                                                                                                   .amount(total)
                                                                                                   .build();
                                                                        })
                                                                        .collect(Collectors.toList());
        return R.ok(currencyAmountVOList);
    }

    /**
     * 仓储费支付
     * @param storageFeePayBo
     */
    @InMethodLog("仓储费支付方法")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> payStorageFee (StorageFeePayBo storageFeePayBo){
        // 校验支付密码
        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(storageFeePayBo.getPaymentPassword()));
        // 校验仓储费状态,判断仓储费的状态是否为确认中
        List<StorageFeeInfo> storageFeeInfoList = this.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, storageFeePayBo.getStorageFeeIds()));
        if(CollUtil.isEmpty(storageFeeInfoList)){
            log.info("仓储费不存在");
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_NOT_EXIST.args(storageFeePayBo.getStorageFeeIds()));
        }
        // 错误状态的仓储费ID
        List<String> errorStorageFeeIdList = new ArrayList<>();
        for (StorageFeeInfo storageFeeInfo : storageFeeInfoList){
            if(!FeeStateEnum.CONFIRMING.getValue().equals(storageFeeInfo.getFeeState())){
                errorStorageFeeIdList.add(storageFeeInfo.getStorageFeeId());
            }
        }
        if(CollUtil.isNotEmpty(errorStorageFeeIdList)){
            return R.fail(ZSMallStatusCodeEnum.STORAGE_FEE_STATE_ERROR.args(errorStorageFeeIdList));
        }

        return R.ok();
    }

    @InMethodLog("获取总仓储费金额")
    public List<CurrencyAmountVO> getTotalStorageFee(Integer feeState,Integer payState){
        LambdaQueryWrapper<StorageFeeInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        if(null !=  tenantTypeEnum){
            if(tenantTypeEnum.equals(TenantType.Distributor)){
                lambdaQueryWrapper.eq(StorageFeeInfo::getTenantId,LoginHelper.getTenantId());
                lambdaQueryWrapper.in(StorageFeeInfo::getFeeState,Arrays.asList(FeeStateEnum.CONFIRMING.getValue(),FeeStateEnum.CONFIRMED.getValue()));
                lambdaQueryWrapper.eq(StorageFeeInfo::getPayState,payState);
            }
            if(tenantTypeEnum.equals(TenantType.Manager)){
                lambdaQueryWrapper.eq(StorageFeeInfo::getFeeState, feeState);
            }
        }
        List<StorageFeeInfo> storageFeeInfoList = new ArrayList<>();
        if(tenantTypeEnum.equals(TenantType.Manager)) {
            storageFeeInfoList = TenantHelper.ignore(() ->this.list(lambdaQueryWrapper));
        }
        if(tenantTypeEnum.equals(TenantType.Distributor)){
            storageFeeInfoList = this.list(lambdaQueryWrapper);
        }
        if(CollUtil.isEmpty(storageFeeInfoList)){
            return null;
        }
        List<CurrencyAmountVO> currencyAmountVOList = storageFeeInfoList.stream()
                                                                        .collect(Collectors.groupingBy(StorageFeeInfo::getCurrencyCode))
                                                                        .entrySet()
                                                                        .stream()
                                                                        .map(entry -> {
                                                                            List<StorageFeeInfo> groupList = entry.getValue();
                                                                            // 计算总金额
                                                                            BigDecimal total = groupList.stream()
                                                                                                        .map(StorageFeeInfo::getTotalStorageFee)
                                                                                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                                                            // 获取currencySymbol（取第一个元素的值，假设同组currencySymbol一致）
                                                                            String currencySymbol = groupList.get(0).getCurrencySymbol();
                                                                            // 构建CurrencyAmountVO对象
                                                                            return CurrencyAmountVO.builder()
                                                                                                   .currency(entry.getKey())
                                                                                                   .currencySymbol(currencySymbol)
                                                                                                   .amount(total)
                                                                                                   .build();
                                                                        })
                                                                        .collect(Collectors.toList());
        return currencyAmountVOList;
    }

}


