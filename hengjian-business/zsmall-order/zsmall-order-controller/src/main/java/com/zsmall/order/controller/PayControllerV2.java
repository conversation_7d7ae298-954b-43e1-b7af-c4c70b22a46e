package com.zsmall.order.controller;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.StringUtils;
import com.zsmall.common.domain.airwallex.payment.intents.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.AirwallexService;
import com.zsmall.order.biz.service.PayService;
import com.zsmall.order.entity.domain.bo.pay.AirwallexPayParam;
import com.zsmall.order.entity.domain.bo.pay.PayoneerPayParam;
import com.zsmall.system.entity.domain.bo.pay.AirwallexOrderPayBo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024年3月6日  10:37
 * @description: 支付
 */
@Slf4j
@RestController
@RequestMapping(value = "/payV2")
@Tag(name = "支付相关接口")
public class PayControllerV2 {

    @Autowired
    PayService payService;

    @Autowired
    AirwallexService airwallexService;

    /**
     * 订单支付
     *
     * @param airwallexOrderPayBo
     * @return
     */
    @PostMapping("orderPay")
    public R orderPay(
        @RequestBody AirwallexOrderPayBo airwallexOrderPayBo
    ) throws Exception {
        if(null != airwallexOrderPayBo && null != airwallexOrderPayBo.getType()){
            if(airwallexOrderPayBo.getType().equals(2)){
                if(StringUtils.isEmpty(airwallexOrderPayBo.getReturnUrl()) || StringUtils.isEmpty(airwallexOrderPayBo.getCancelUrl())){
                    return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
                }
                return R.ok(payService.orderPayByPayoneer(airwallexOrderPayBo));
            }
            if(airwallexOrderPayBo.getType().equals(1)){
                return R.ok(payService.orderPay(airwallexOrderPayBo));
            }
        }
        return R.ok();
    }

    /**
     * 充值
     *
     * @param airwallexOrderPayBo
     * @return
     */
    @PostMapping("rechargePay")
    public R rechargePay(
        @RequestBody AirwallexOrderPayBo airwallexOrderPayBo
    ){
        return R.ok(payService.rechargePay(airwallexOrderPayBo));
    }

    @PostMapping("payoneerPayTest")
    public R payoneerPayTest(){
        payService.payoneerPayTest();
        return R.ok();
    }

    @PostMapping("airwallexPayStorageTest")
    public R airwallexPayStorageTest(){
        AirwallexPayParam airwallexPayParam = new AirwallexPayParam();
        airwallexPayParam.setAmount(new BigDecimal(0.01));
        airwallexPayParam.setCurrency("USD");
        airwallexPayParam.storageFeeInit("<EMAIL>","16985748596",new BigDecimal(0.01));
        airwallexPayParam.setRelatesInfo(Arrays.asList("1"));
        return R.ok(payService.airwallexPay(airwallexPayParam));
    }

    @PostMapping("payoneerPayStorageTest")
    public R payoneerPayStorageTest() throws Exception {
        PayoneerPayParam payoneerPayParam = new PayoneerPayParam();
        payoneerPayParam.storageFeeInit("https://distribution-pr.ehengjian.com/backend/order/orders-list?type=SUCCESS_URL&channel=payoneer",
            "https://distribution-pr.ehengjian.com/backend/order/orders-list?type=FAIL_URL&channel=payoneer",new BigDecimal("0.01"),"USD","<EMAIL>",new BigDecimal("0.01"),Arrays.asList("1111111"));
        return R.ok(payService.payoneerPay(payoneerPayParam));
    }
}
