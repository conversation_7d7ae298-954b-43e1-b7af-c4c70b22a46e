package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.impl.SysOssServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.AttachInfo;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.tiktok.domain.dto.address.SiteMsgBo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokDistrictInfo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.warehouse.WarehouseChannelCodeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.manager.OrderAndItemManager;
import com.zsmall.order.biz.service.DistributorOrderService;
import com.zsmall.order.biz.service.OrderItemPriceService;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderActivitySupport;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.biz.support.WholesaleOrderSupport;
import com.zsmall.order.biz.utils.AttachmentServiceHandler;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.order.OrderFlowEnum.THIRD_CREATE_ORDER;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/13 13:48
 */
@Slf4j
@Lazy
@Component("openOperationHandler")
public class OpenOrderOperationHandler extends AbstractOrderOperationHandler<String, OrderReceiveFromThirdDTO, Map<String, Object>, Orders, Object> {
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;

    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;
    @Resource
    private OrderCodeGeneratorV2 orderCodeGeneratorV2;
    @Resource
    private AttachmentServiceHandler attachmentServiceHandler;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private PriceSupportV2 priceSupportV2;
    @Resource
    private OrderItemPriceService orderItemPriceService;
    @Resource
    private IProductMappingService iProductMappingService;
    @Resource
    private SysOssMapper sysOssMapper;
    @Resource
    private BillSupport billSupport;
    @Resource
    private IOrderRefundService iOrderRefundService;
    @Resource
    private WholesaleOrderSupport wholesaleSupports;
    @Resource
    private ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    @Resource
    private IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    @Resource
    private DistributorOrderService distributorOrderService;
    @Resource
    private IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    //    @XxlConf(value = "distribution.shipping.address.id.erp",defaultValue = "1704748687534034946")

    @Resource
    private SysOssServiceImpl sysOssService;
    @Resource
    private ITenantShippingAddressService tenantShippingAddressService;

    //    @XxlConf(value = "distribution.specify.warehouse.id.hj",defaultValue = "BG94930")
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String warehouseSystemCode;

    @Resource
    private IOrderItemShippingRecordService iOrderItemShippingRecordService;
    @Resource
    private OrderItemService orderItemService;

    @Resource
    private OrderSupport orderSupport;

    @Resource
    private IProductSkuService iProductSkuService;

    @Resource
    private ISysTenantService sysTenantService;

    @Resource
    private IProductService iProductService;

    @Resource
    private OrderAndItemManager orderAndItemManager;

    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private OrdersService ordersService;

    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;

    @Resource
    private IConfZipService iConfZipService;

    @Resource
    private BusinessParameterService businessParameterService;

    @Autowired
    ApplicationContext applicationContext;

    @Resource
    private IWorldLocationService iWorldLocationService;

    @Resource
    private OrderCodeGenerator orderCodeGenerator;

    @Resource
    private IProductSkuAttachmentService iProductSkuAttachmentService;
    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;


    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;


    @Resource
    private IProductSkuService productSkuService;

    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;
    @Resource
    private OrderActivitySupport orderActivitySupport;
    @Value("${distribution.open.pay.async}")
    public Boolean openPayIsNeedAsync;
    @Override
    public void initialMessageSave(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {

    }

    @Override
    public OrderReceiveFromThirdDTO parseThirdData(String json) throws Exception {
        OrderReceiveFromThirdDTO erpDTO = JSONObject.parseObject(json, OrderReceiveFromThirdDTO.class);
        Integer isNeedLabeling = erpDTO.getIsNeedLabeling();
        if(ObjectUtil.isEmpty(isNeedLabeling)){
            erpDTO.setIsNeedLabeling(1);
        }
        if (ObjectUtil.isEmpty(erpDTO)) {
            throw new AppRuntimeException("订单数据不能为null");
        }


        return erpDTO;
    }

    @Override
    public Boolean msgVerify(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        // 渠道单号重复Set
        orderReceiveFromThirdDTO.getSaleOrderItemsList();

        Set<String> channelOrderNoSet = new HashSet<>();

        List<SaleOrderItemDTO> orderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();

        for (SaleOrderItemDTO itemDTO : orderItemsList) {
            ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(itemDTO.getErpSku(), WarehouseChannelCodeEnum.TIKTOK.getWarehouseSystemCode());
            // 仓库号,可以通过渠道写死 提前配置好仓库

            if (productSku == null) {

                continue;
            } else {
                // 如果商品被管控，则报商品不存在
                String tenantId = "1";
                boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, itemDTO.getErpSku(), ChannelTypeEnum.Others.name());

                if (!checkUserAllow) {
                    // 日志记录商品不存在
                    return Boolean.FALSE;
                }
            }

            Product product = iProductService.queryByProductSkuCode(itemDTO.getErpSku());
            if (StrUtil.isNotBlank(orderReceiveFromThirdDTO.getOrderNo())) {
                if (channelOrderNoSet.contains(orderReceiveFromThirdDTO.getOrderNo())) {
                    // ?
                } else {
                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
                    boolean orderExists = iOrdersService.existsChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo(), OrderStateType.Canceled);
                    // 查询导入缓存表
                    if (orderExists) {
                        return Boolean.FALSE;
                        // 存在同一个订单
                    } else {
                        channelOrderNoSet.add(orderReceiveFromThirdDTO.getOrderNo());
                    }
                }
            }
            // 规则校验
            if (!RegexUtil.matchQuantity(itemDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }


            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            // 三方默认代发
            String logisticsType = "DropShipping";
            if (StrUtil.equals(logisticsType, "DropShipping")) {
                // 仅支持自提
                if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
                    // 商品仅支持自提
                    log.info("订单与商品物流类型冲突,订单号:{},商品sku:{},商品仅支持自提", orderReceiveFromThirdDTO.getOrderNo(), itemDTO.getErpSku());
                    return Boolean.FALSE;
                }
            }


        }
        return Boolean.TRUE;
    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        for (OrderItem item : orderItems) {
            for (OrderItemProductSku sku : skus) {
                if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                    sku.setOrderItemId(item.getId());
                    sku.setOrderNo(item.getOrderNo());
                }
            }
        }
        log.info("订单操作完成,进行保存操作");
        iOrderItemProductSkuService.saveBatch(skus);
        return orders;
    }

    @Override
    public void formalOrderAboutEntry(Map<String, Object> map) {
        List<OrderLogisticsInfo> logisticsInfo = (List<OrderLogisticsInfo>) map.get("logisticsInfo");
        List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
        Orders orders = (Orders) map.get("orders");
        String tenantId = orders.getTenantId();
        iOrderLogisticsInfoService.saveBatch(logisticsInfo);
        iOrderAddressInfoService.saveBatch(address);
        String warehouseCode = null;
//      address转换为map,key为orderNo,value为zipCode
        Map<String, String> addressMap = address.stream()
                                                .collect(Collectors.toMap(OrderAddressInfo::getOrderNo, OrderAddressInfo::getZipCode));
        String logisticsCompanyName = null;
        if(CollUtil.isNotEmpty(logisticsInfo)){
            OrderLogisticsInfo orderLogisticsInfo = logisticsInfo.get(0);
            if (ObjectUtil.isNotEmpty(orderLogisticsInfo)){
                logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
            }
        }
        // 此处已经生成了itemProductSku需要重新选仓库
        String warehouseSystemCode = null ;
        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            // 重新计算订单价格信息
            List<OrderItemPrice> itemPrices = new ArrayList<>();
            List<OrderItem> orderItemUpdateList = new ArrayList<>();
            List<OrderItem> listByOrderId = iOrderItemService.getListByOrderId(orders.getId());
            // 查询明细商品关联
            HashMap<String,List<String> >stashMap = orderSupport.getStashList(listByOrderId);
            if(CollUtil.isNotEmpty(listByOrderId)){
                warehouseSystemCode = priceHandle(listByOrderId, orders, stashMap, tenantId, addressMap, logisticsCompanyName, warehouseSystemCode, itemPrices, orderItemUpdateList);

            }

            // 更新订单明细信息
            orderItemPriceService.saveOrSetNUll(itemPrices,orders.getExceptionCode());
            orderItemService.updateOrSetNUll(orderItemUpdateList,orders.getExceptionCode());
            // orders 需要二次更新,第一次更新基础信息,第二次更新价格信息
            iOrdersService.saveOrUpdate(orders);
            ordersService.updateOrSetNull(orders);
            iOrderItemProductSkuService.updateOrSetNull(orders.getOrderNo(),warehouseSystemCode,warehouseSystemCode);

            log.info(orders.toString());
        }
    }

    /**
     * 功能描述：价格手柄
     *
     * @param listByOrderId        按订单id列出
     * @param orders               订单
     * @param stashMap             藏匿地图
     * @param tenantId             租户id
     * @param addressMap           地址图
     * @param logisticsCompanyName 物流公司名称
     * @param warehouseSystemCode  仓库系统代码
     * @param itemPrices           商品价格
     * @param orderItemUpdateList  订单项目更新列表
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/11/14
     */
    private String priceHandle(List<OrderItem> listByOrderId, Orders orders, HashMap<String, List<String>> stashMap,
                               String tenantId, Map<String, String> addressMap, String logisticsCompanyName,
                               String warehouseSystemCode, List<OrderItemPrice> itemPrices,
                               List<OrderItem> orderItemUpdateList) {
        for(OrderItem orderItem : listByOrderId){
            OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
            orderPriceCalculateDTO.setOrderItem(orderItem);
            orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
            orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
            List<String> stashList = stashMap.get(orderItem.getOrderItemNo());
            // 仓库编号入口
            LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO, tenantId, addressMap.get(orderItem.getOrderNo()),stashList, orders, THIRD_CREATE_ORDER, logisticsCompanyName);

            warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();

            if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
            }
            OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
            OrderItemPrice orderItemPrice = new OrderItemPrice();
            BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
            orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(orderItem.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
            itemPrices.add(orderItemPrice);
            OrderItem orderItemUpdate = new OrderItem();
            BeanUtils.copyProperties(orderItem, orderItemUpdate);
            orderItemUpdateList.add(orderItemUpdate);
        }
        // 重新计算主订单数据
        priceSupportV2.recalculateOrderAmount(orders, itemPrices);
        return warehouseSystemCode;
    }

    @Override
    public Map<String, Object> msgForLogistics(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        OrderItem item = orderItems.get(0);
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();
        ArrayList<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        SaleOrderDetailDTO details = orderReceiveFromThirdDTO.getSaleOrderDetails();
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        SaleOrderItemDTO dto = saleOrderItemsList.get(0);

        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();

        for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {
            // 拿默认地址模版
            List<TikTokDistrictInfo> districtInfo = address.getDistrictInfo();
            String country = null;
            String state = null;
            String federalDistrict = null;
            String county = null;
            String city = null;

            for (TikTokDistrictInfo tikTokDistrictInfo : districtInfo) {

                String levelName = tikTokDistrictInfo.getAddressLevelName();
                String addressName = tikTokDistrictInfo.getAddressName();
                if ("country".equalsIgnoreCase(levelName)) {
                    country = addressName;
                }
                if ("state".equalsIgnoreCase(levelName)) {
                    state = addressName;
                }
                if ("Federal District".equalsIgnoreCase(levelName)) {
                    federalDistrict = addressName;
                }
                if ("county".equalsIgnoreCase(levelName)) {
                    county = addressName;
                }
                if ("city".equalsIgnoreCase(levelName)) {
                    city = addressName;
                }
            }

            ConfZip confZip;
            confZip = iConfZipService.getStateCodeByStateCode(state);
            if (ObjectUtil.isEmpty(confZip)) {
                confZip = iConfZipService.getStateCodeByCity(city);
            }

            OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
            orderAddressInfo.setCounty(county);
            orderAddressInfo.setOrderId(orders.getId());
            orderAddressInfo.setOrderNo(orders.getOrderNo());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
            // 拿默认模版里面
            orderAddressInfo.setRecipient(address.getName());

            orderAddressInfo.setPhoneNumber(address.getPhoneNumber());
            // 这三个信息需要调用包裹接口拿到详细的包裹信息
            orderAddressInfo.setCountry(country);
            orderAddressInfo.setCountryCode(address.getRegionCode());
            String zipCode = null;
            if (ObjectUtil.isNotEmpty(state)) {
                zipCode = iConfZipService.transZip(address.getPostalCode(), state, county, city);
                orderAddressInfo.setState(state);
            }
            if (ObjectUtil.isNotEmpty(federalDistrict)) {
                zipCode = iConfZipService.transZip(address.getPostalCode(), federalDistrict, county, city);
                orderAddressInfo.setState(federalDistrict);
            }
            if(ObjectUtil.isEmpty(confZip)){
                //addressName 给的 CA
                orderAddressInfo.setStateCode(state);
            }else{
                orderAddressInfo.setStateCode(confZip.getStateCode());
            }


            orderAddressInfo.setCity(city);
            orderAddressInfo.setAddress1(address.getAddressLine1());
            orderAddressInfo.setAddress2(address.getAddressLine2());
            orderAddressInfo.setAddress3(address.getAddressLine3());

            orderAddressInfo.setZipCode(zipCode);
            orderAddressInfo.setEmail(address.getBuyerEmail());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

            addressInfos.add(orderAddressInfo);
            orderLogisticsInfo.setLogisticsCarrierCode(details.getCarrier());
            orderLogisticsInfo.setOrderId(orders.getId());
            orderLogisticsInfo.setOrderNo(orders.getOrderNo());
            orderLogisticsInfo.setShippingLabelExist(true);
            orderLogisticsInfo.setLogisticsZipCode(zipCode);
            orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
            orderLogisticsInfo.setLogisticsCompanyName(details.getCarrier());
            orderLogisticsInfo.setShipServiceLevel(details.getShipServiceLevel());
            orderLogisticsInfo.setLogisticsCountryCode(address.getRegionCode());
            orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
            orderLogisticsInfo.setZipCode(zipCode);
            orderLogisticsInfos.add(orderLogisticsInfo);
        }
        if(StrUtil.isNotEmpty(details.getLogisticsTrackingNo())){
            String trimTracking = StrUtil.trim(details.getLogisticsTrackingNo());
            String[] split = trimTracking.split(",");
            for (String trackNo : split) {
                if (StrUtil.isNotBlank(trackNo)) {
                    OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
                    trackingRecord.setSku(dto.getErpSku());
                    trackingRecord.setProductSkuCode(item.getProductSkuCode());
                    trackingRecord.setLogisticsCarrier(details.getCarrier());
                    trackingRecord.setLogisticsTrackingNo(trackNo);
                    trackingRecord.setOrderNo(orders.getOrderNo());
                    trackingRecord.setOrderItemNo(item.getOrderItemNo());
                    trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
                    // 用的bizArk仓库ID
                    trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
                    if (ObjectUtil.equal(orderReceiveFromThirdDTO.getLogisticsType(), LogisticsTypeEnum.PickUp.name())){
                        String warehouseCode = orderReceiveFromThirdDTO.getSaleOrderDetails().getWarehouseCode();
                        trackingRecord.setWarehouseCode(warehouseCode);
                        String warehouseSystemCode = warehouseService.queryByWarehouse(warehouseCode, item.getSupplierTenantId());
                        trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
                    }
                    trackingRecord.setQuantity(item.getTotalQuantity());
                    iOrderItemTrackingRecordService.save(trackingRecord);

                }else {
                    OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
                    trackingRecord.setSku(dto.getErpSku());
                    trackingRecord.setProductSkuCode(item.getProductSkuCode());
                    trackingRecord.setLogisticsCarrier(details.getCarrier());
                    trackingRecord.setLogisticsTrackingNo(trackNo);
                    trackingRecord.setOrderNo(orders.getOrderNo());
                    trackingRecord.setOrderItemNo(item.getOrderItemNo());
                    trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
                    // 用的bizArk仓库ID
                    trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
                    if (ObjectUtil.equal(orderReceiveFromThirdDTO.getLogisticsType(), LogisticsTypeEnum.PickUp.name())){
                        String warehouseCode = orderReceiveFromThirdDTO.getSaleOrderDetails().getWarehouseCode();
                        trackingRecord.setWarehouseCode(warehouseCode);
                        String warehouseSystemCode = warehouseService.queryByWarehouse(warehouseCode, item.getSupplierTenantId());
                        trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
                    }
                    trackingRecord.setQuantity(item.getTotalQuantity());
                    iOrderItemTrackingRecordService.save(trackingRecord);
                }
            }
        }else {
            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
            trackingRecord.setSku(dto.getErpSku());
            trackingRecord.setProductSkuCode(item.getProductSkuCode());
            trackingRecord.setLogisticsCarrier(details.getCarrier());
            trackingRecord.setLogisticsTrackingNo(details.getLogisticsTrackingNo());
            trackingRecord.setOrderNo(orders.getOrderNo());
            trackingRecord.setOrderItemNo(item.getOrderItemNo());
            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 用的bizArk仓库ID
            trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
            if (ObjectUtil.equal(orderReceiveFromThirdDTO.getLogisticsType(), LogisticsTypeEnum.PickUp.name())){
                String warehouseCode = orderReceiveFromThirdDTO.getSaleOrderDetails().getWarehouseCode();
                trackingRecord.setWarehouseCode(warehouseCode);
                String warehouseSystemCode = warehouseService.queryByWarehouse(warehouseCode, item.getSupplierTenantId());
                trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
            }
            trackingRecord.setQuantity(item.getTotalQuantity());
            iOrderItemTrackingRecordService.save(trackingRecord);
        }

        List<OrderItemPrice> itemPrices = new ArrayList<>();
        OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
        // 物流跟踪单

        for (OrderItem orderItem : orderItems) {
            paramDTO.setOrderItem(orderItem);
            paramDTO.setSiteId(orders.getSiteId());
            paramDTO.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()));
            OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForThird(paramDTO);
            OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
            itemPrice.setOrderItemId(orderItem.getId());
            itemPrices.add(itemPrice);
        }

        map.put("logisticsInfo", orderLogisticsInfos);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        map.put("orders", orders);
        log.info("物流信息:{}", JSONUtil.toJsonStr(orderLogisticsInfo));
        return map;
    }

    @Override
    public Map<String, List> msgForItems(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();

        for (SaleOrderItemDTO saleOrderItemDTO : saleOrderItemsList) {
            OrderItem orderItem = new OrderItem();
            orderItem.setChannelType(orders.getChannelType());
            // 获取商品相关的活动信息
            DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(orderReceiveFromThirdDTO.getTenantId(), saleOrderItemDTO.getItemNo(), orders.getCountryCode(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
            // 判断活动订单
            if (null != distributorProductActivity) {
                orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orderItem.setActivityType(distributorProductActivity.getActivityType());
                orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orders.setActivityType(distributorProductActivity.getActivityType());
            }
            orderItemService.setOrderBusinessFieldForOpen(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            orderItemService.setChannelTagForOpen(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);

            iOrderItemThirdService.setOrderTagSystemForOpen(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            orderItems.add(orderItem);
            OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
            // 通过 自订单编号进行关联,
            orderItemProductSkuThirdService.setBusinessFieldForOpen(orderItemProductSku, orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            // 活动订单设置为活动仓库
            if (null != distributorProductActivity) {
                DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
                if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                    orderItemProductSku.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                    orderItemProductSku.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
                }

            }
            orderItemProductSkus.add(orderItemProductSku);
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        log.info("订单项信息:{}", JSONUtil.toJsonStr(orderItems));
        return hashMap;
    }

    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {

    }

    @Override
    public Boolean isNeedPay() {
        return Boolean.TRUE;
    }

    @Override
    public String attachmentsFlow(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,Orders orders) {
        LogisticsTypeEnum logisticsType = orders.getLogisticsType();
        String orderExtendId = orderReceiveFromThirdDTO.getOrderExtendId();
        if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
            SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
            List<AttachInfo> attachInfoItems = saleOrderDetails.getAttachInfoItems();
            for (int i=0;i<attachInfoItems.size();i++){
                AttachInfo attachInfo = attachInfoItems.get(i);
                attachmentServiceHandler.ossUploadMethod(orders, attachInfo, saleOrderDetails, orderExtendId,i);
            }

        }

        return orders.getOrderExtendId();
    }


//    private void ossUploadMethod(Orders orders, AttachInfo attachInfo, SaleOrderDetailDTO saleOrderDetails,
//                                 String orderExtendId, int i) {
//        Integer fileType = attachInfo.getFileType();
//        OrderAttachmentTypeEnum typeEnum = OrderAttachmentTypeEnum.getByCode(fileType);
//        String carrier = saleOrderDetails.getCarrier();
//        // 按照承运商类型区分,如果是 LTL的 附件只走一个
//        // LTL BOL附件只走一个,label会有多个,但是多个订单用一个附件的ossId
//        if (CarrierTypeEnum.LTL.getValue().equals(carrier)) {
//            // bol 只上传一次 通过唯一标识来判断
//            // 考虑直接上锁,同一个行id,就上传一次
//            // 从key里拿value
//            String lockKey =null;
//            String ossKey =null;
//            if(fileType==0){
//                lockKey =  "oss"+":"+"BOL:"+ orderExtendId;
//                ossKey = "oss:key"+":"+"BOL:"+ orderExtendId;
//            }
//            if(fileType==1){
//                lockKey =  "oss"+":"+"ShippingLabel:"+i+":"+orderExtendId;
//                ossKey = "oss:key"+":"+"ShippingLabel:"+i+":"+ orderExtendId;
//            }
//
//            RLock lock = redissonClient.getLock(lockKey);
//            SysOssVo sysOssVo = null;
//            boolean locked = false;
//            try {
//                locked = lock.tryLock(10, TimeUnit.MINUTES);
//                if (locked) {
//                    try {
//                        RBucket<String> bucket = redissonClient.getBucket(ossKey);
//                        String value = bucket.get();
//                        if (ObjectUtil.isEmpty(value)) {
////                                    sysOssVo = sysOssService.downloadPdfNotAsyncForOpen(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
//                            sysOssVo = sysOssService.downloadPdfNotAsync(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
//                            value = sysOssVo.getOssId()+"-"+sysOssVo.getSavePath();
//                            bucket.set(value, 20, TimeUnit.MINUTES);
//                        } else {
//                            String[] parts = value.split("-");
//                            Long ossId  = Long.valueOf( parts[0]);
//                            String savePath = parts[1];
//                            // 还要适配多附件的情况
//                            sysOssVo = sysOssService.getById(ossId);
//                            sysOssVo.setSavePath(savePath);
//                        }
//                    } finally {
//                        lock.unlock();
//                    }
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt(); // 恢复中断状态
//                throw new RuntimeException("Lock acquisition interrupted", e);
//            }
//            OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo,typeEnum);
//            distributorOrderService.uploadShippingLabelNotBatch(bo);
//        }else {
//            // 其他类型只会有一个
//            log.info("附件信息:{}", JSONUtil.toJsonStr(attachInfo));
//            SysOssVo sysOssVo = sysOssService.downloadPdfNotAsyncForOpen(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
////                SysOssVo sysOssVo = sysOssService.downloadPdfNotAsync(attachInfoItem.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
//
//            if(ObjectUtil.isEmpty(sysOssVo)){
//                throw new RuntimeException("附件信息异常:"+ attachInfo.getUrl());
//            }
//            OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo);
//            // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
//            distributorOrderService.uploadShippingLabelNotBatch(bo);
//        }
//    }

    @Override
    public Boolean isNeedPay(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        return null;
    }

    @Override
    public Boolean payOrder(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderReceiveFromThirdDTO.getOrderNo());

            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            try {
                TenantHelper.ignore(() -> {
                    try {
                        return ordersService.payOrderForErp(bo, orderReceiveFromThirdDTO.getTenantId(), true, true);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                log.error("支付失败:{}", e.getMessage());
            }

        }
        return true;
    }

    /**
     * 功能描述：支付订单打开
     *
     * @param orderReceiveFromThirdDTO 从第三个数据仓库收到订单
     * @param isAsync                  是否需要异步-消息队列
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/11/05
     */
    @Override
    public Boolean payOrderForAsync(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Boolean isAsync) throws Exception {
        // 是否走异步,从配置文件里拿
        if(openPayIsNeedAsync&&isAsync){
            String json = JSONObject.toJSONString(orderReceiveFromThirdDTO);
            rabbitTemplate.convertAndSend(RabbitMqConstant.OPEN_PAY_EXCHANGE,RabbitMqConstant.OPEN_PAY_KEY,json);
        }else {
            // 是否开启自动支付
            TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
            String currencyCode = address.getSiteBo().getCurrencyCode();
            Boolean aBoolean = ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(orderReceiveFromThirdDTO.getTenantId(), currencyCode);
            if (Boolean.TRUE.equals(aBoolean)) {
                LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderReceiveFromThirdDTO.getOrderNo());

                List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                    .collect(Collectors.toList());
                OrderPayBo bo = new OrderPayBo();
                bo.addOrderNoList(orderNos);
                bo.setPaymentPassword(null);
                if(CollUtil.isEmpty(orderNos)){
                    return false;
                }
                try {
                    TenantHelper.ignore(() -> {
                        try {
                            SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
                            String isNeedDelivery = null;
                            if(ObjectUtil.isEmpty(saleOrderDetails.getIsNeedDelivery())){
                                isNeedDelivery = "1";
                            }else {
                                isNeedDelivery = String.valueOf(saleOrderDetails.getIsNeedDelivery());
                            }
                            return ordersService.payOrderForDistribution(bo, orderReceiveFromThirdDTO.getTenantId(), true, "1".equals(isNeedDelivery));
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error(e.getMessage());
                            throw new RuntimeException(e.getMessage());
                        }
                    });
                } catch (Exception e) {
                    log.error("支付失败:{}", e.getMessage());
                }

            }
        }
        return true;
    }

    @Override
    public Orders thirdToDistribution(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
//      v1版本 存在并发问题  String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        Orders order = new Orders();
        Integer isNeedLabeling = orderReceiveFromThirdDTO.getIsNeedLabeling();
        order.setChannelType(orders.getChannelType());
        String distributionOrderNo = orderReceiveFromThirdDTO.getDistributionOrderNo();
        String orderExtendId = orderReceiveFromThirdDTO.getOrderExtendId();
        order.setIsNeedLabeling(isNeedLabeling);
        order.setOrderNo(distributionOrderNo);
        order.setOrderExtendId(orderExtendId);
        order.setOrderSource(OrderSourceEnum.OPEN_API_ORDER.getValue());
        // 业务属性
        order = ordersService.setOrderBusinessFieldForOpen(orderReceiveFromThirdDTO, order);
        order = ordersService.setOrderTagSystem(orderReceiveFromThirdDTO, order);
        order = ordersService.setChannelTagForOpen(orderReceiveFromThirdDTO, order);

        return order;
    }

    @Override
    public List<OrderReceiveFromThirdDTO> ordersDisassemble(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
        String regionCode = address.getRegionCode();
        SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
        List<OrderReceiveFromThirdDTO> tikDTOS = new ArrayList<>();
        // 需要通过itemOrderId进行搜索
        LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderReceiveFromThirdDTO.getOrderNo())
                                                                         .eq(Orders::getDelFlag, 0).last("limit 1");

        Orders order = iOrdersService.getOne(lqw);
        if (ObjectUtil.isNotEmpty(order)) {
            throw new AppRuntimeException("订单已经录入" + orderReceiveFromThirdDTO.getOrderNo());
        }
        SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();

        // 对saleOrderDetails 的仓库编码进行校准.
        String warehouseCode = saleOrderDetails.getWarehouseCode();



        // 把订单项拆开
        String productTenantId=null;
        List<String> skuCodes = saleOrderItemsList.stream().map(SaleOrderItemDTO::getErpSku)
                                                 .collect(Collectors.toList());
        // 是否可能存在外部的sku名称一致
        LambdaQueryWrapper<ProductSku> wrapper = new LambdaQueryWrapper<ProductSku>().in(ProductSku::getProductSkuCode, skuCodes).eq(ProductSku::getDelFlag,0);
        List<ProductSku> skus = TenantHelper.ignore(()->iProductSkuService.list(wrapper));
        boolean hasDifferentTenantIds = skus.stream()
                                            .map(ProductSku::getTenantId)
                                            .collect(Collectors.toSet())
                                            .size() > 1;
        if(hasDifferentTenantIds){
            throw new RuntimeException("The SKUs on the order correspond to different owners, so the order cannot be created");
        }
        // 此处需要生成订单id, 和子订单id.
        String orderExtendId = orderCodeGeneratorV2.generateOrderNumber(BusinessCodeEnum.NewOrderNo);
        //临时方案，让非PO单业务正常走通
        String productSKuCode=saleOrderItemsList.get(0).getErpSku();


        SaleOrderItemDTO dto = saleOrderItemsList.get(0);
        ProductSku forTenantId = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>()
            .eq(ProductSku::getProductSkuCode, dto.getErpSku())
            .eq(ProductSku::getDelFlag, 0));
        productTenantId = forTenantId.getTenantId();
        if(ObjectUtil.isNotEmpty(warehouseCode)){
            LambdaQueryWrapper<Warehouse> query = new LambdaQueryWrapper<>();

            query.eq(Warehouse::getWarehouseCode, warehouseCode);
            query.eq(Warehouse::getTenantId,productTenantId);
            query.eq(Warehouse::getWarehouseState, 1);
            query.eq(Warehouse::getDelFlag, 0);
            Warehouse warehouse = warehouseService.getBaseMapper().selectOne(query);
            if(ObjectUtil.isEmpty(warehouse)){
                query = new LambdaQueryWrapper<>(); // 重置查询条件
                query.eq(Warehouse::getWarehouseSystemCode, saleOrderDetails.getWarehouseCode()) // 关键字段替换
                     .eq(Warehouse::getTenantId, productTenantId)
                     .eq(Warehouse::getWarehouseState, 1)
                     .eq(Warehouse::getDelFlag, 0);
                warehouse = warehouseService.getBaseMapper().selectOne(query);
                if(ObjectUtil.isNotEmpty(warehouse)){
                    String warehouseCode1 = warehouse.getWarehouseCode();
                    saleOrderDetails.setWarehouseCode(warehouseCode1);
                    // 仓库编码校准
                    warehouseCode = warehouseCode1;
                }
            }
        }

        for(int i =0;i<saleOrderItemsList.size();i++){
            SaleOrderItemDTO itemDTO = saleOrderItemsList.get(i);
            List<SaleOrderItemDTO> saleOrderItemDTOS = new ArrayList<>();
            OrderReceiveFromThirdDTO erpDTO = new OrderReceiveFromThirdDTO();
            BigDecimal unitPrice = itemDTO.getUnitPrice();
            Integer quantity = itemDTO.getQuantity();
            String totalAmount = String.valueOf(unitPrice.multiply(BigDecimal.valueOf(quantity)));
            int i1 = i + 1;
            ProductSku one = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>()
                .eq(ProductSku::getProductSkuCode, itemDTO.getErpSku())
                .eq(ProductSku::getDelFlag, 0));
            if (ObjectUtil.isEmpty(one)) {
                log.error("订单:{},产品尚未更新映射,或sku信息异常,请检查sku是否存在",orderReceiveFromThirdDTO.getOrderNo());
                throw new AppRuntimeException("产品:"+productSKuCode+",尚未更新映射,或sku信息异常,请检查sku是否存在");
            }
            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSKuCode, site.getId());
            if (ObjectUtil.isEmpty(productSkuPrice)){
                throw new AppRuntimeException("产品:"+productSKuCode+",站点价格不存在,请确认站点信息或联系管理员");
            }
            productTenantId=one.getTenantId();
            // 这里直接拿product_sku_code 来比较好
            itemDTO.setItemNo(one.getProductSkuCode());
            saleOrderItemDTOS.add(itemDTO);
            BeanUtils.copyProperties(orderReceiveFromThirdDTO, erpDTO);
//            erpDTO.setSubTotal(totalAmount);
            erpDTO.setTotalAmount(totalAmount);
            erpDTO.pushSaleOrderItemsList(saleOrderItemDTOS);

            erpDTO.setOrderExtendId(orderExtendId);
            erpDTO.setTotalQuantity(quantity);
            if(saleOrderItemsList.size()==1) {
                erpDTO.setDistributionOrderNo(orderExtendId);
            }else {
                erpDTO.setDistributionOrderNo(orderExtendId+"-"+i1);
            }
            TikTokRecipientAddress erpDTOAddress = erpDTO.getAddress();
            SiteMsgBo siteMsgBo = new SiteMsgBo();

            BeanUtils.copyProperties(site, siteMsgBo);
            siteMsgBo.setSiteId(site.getId());
            erpDTOAddress.setSiteBo(siteMsgBo);
            erpDTO.setLineOrderItemId(orderReceiveFromThirdDTO.getLineOrderItemId());
            tikDTOS.add(erpDTO);
        }

        //如果是自提单,需要校验仓库编码合法性
        if (ObjectUtil.equal(orderReceiveFromThirdDTO.getLogisticsType(), LogisticsTypeEnum.PickUp.name())){
            if (StrUtil.isBlank(warehouseCode)){
                //查询当前商品有库存的仓库编码
                List<WarehouseVo> inStockWarehouseBySku = productSkuService.getInStockWarehouseBySku(productSKuCode, 1, null);
                if (CollUtil.isNotEmpty(inStockWarehouseBySku)){
                    saleOrderDetails.setWarehouseCode(inStockWarehouseBySku.get(0).getWarehouseCode());
                }else {
                    throw new RuntimeException(StrUtil.format("当前商品所有仓库无库存：{}", saleOrderDetails));
                }

            }else {
                LambdaQueryWrapper<Warehouse> o = new LambdaQueryWrapper<>();
                o.eq(Warehouse::getWarehouseCode, warehouseCode);
                o.eq(Warehouse::getTenantId,productTenantId);
                o.eq(Warehouse::getWarehouseState, 1);
                o.eq(Warehouse::getDelFlag, 0);
                Warehouse warehouse = warehouseService.getBaseMapper().selectOne(o);
                if (ObjectUtil.isNull(warehouse)) {
                    throw new RuntimeException(StrUtil.format("非法的仓库编码：{}", saleOrderDetails));
                }
            }

        }
        // 如果 品属于两个供应商就异常.

        return tikDTOS;
    }

    @Override
    public R tripartiteUpdate(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteReceiptGoods(Object o) {
        return null;
    }

    @Override
    public R<Void> test() {
        return null;
    }

    @Override
    public void orderOperationHandler(OrderReceiveFromThirdDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                          ConcurrentHashMap<String, List<Object>> businessMap) {

    }

    @Override
    public List<OrderReceiveFromThirdDTO> ordersDisassembleForList(List<OrderReceiveFromThirdDTO> list,
                                                                   BusinessTypeMappingEnum mappingEnum) {
        return null;
    }

    @Override
    public List<OrderReceiveFromThirdDTO> parseThirdDataForList(String s) {
        return null;
    }
}
