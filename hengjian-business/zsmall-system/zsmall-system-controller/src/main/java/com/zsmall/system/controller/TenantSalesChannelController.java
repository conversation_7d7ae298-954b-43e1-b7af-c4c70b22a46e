package com.zsmall.system.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.service.ISysConfigService;
import com.zsmall.activity.biz.support.StorageFeeSupport;
import com.zsmall.activity.entity.domain.StorageFeeInfo;
import com.zsmall.activity.entity.domain.TransactionStorageFee;
import com.zsmall.activity.entity.iservice.IStorageFeeInfoService;
import com.zsmall.activity.entity.iservice.ITransactionStorageFeeService;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.order.OrderPayTypeEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.payment.PayTypeEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.storageFee.FeeStateEnum;
import com.zsmall.common.enums.storageFee.PayStateEnum;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.AirwallexRequestInfo;
import com.zsmall.order.entity.domain.AirwallexRequestLog;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IAirwallexRequestInfoService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.system.biz.service.PaymentExtensionService;
import com.zsmall.system.biz.service.TenantSalesChannelService;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.payment.PaymentExtensionBo;
import com.zsmall.system.entity.domain.bo.salesChannel.*;
import com.zsmall.system.entity.domain.vo.salesChannel.ChannelGroupListVo;
import com.zsmall.system.entity.domain.vo.salesChannel.SaleChannelCallBackVo;
import com.zsmall.system.entity.domain.vo.salesChannel.TenantSalesChannelVo;
import com.zsmall.system.entity.iservice.IChannelWarehouseInfoServiceImpl;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.iservice.ITransactionRecordService;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import io.swagger.annotations.ApiImplicitParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.BufferedOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zsmall.common.util.AESUtil.decrypt;

/**
 * 租户渠道
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/distributor/salesChannel")
@Slf4j
public class TenantSalesChannelController extends BaseController {

    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final TenantSalesChannelService tenantSalesChannelService;
    private final IAirwallexRequestInfoService iAirwallexRequestInfoService;
    private final PaymentExtensionService paymentExtensionService;
    private final OrderSupport orderSupport;
    private final IOrdersService iOrdersService;
    private final ISysConfigService sysConfigService;
    private final RabbitTemplate rabbitTemplate;
    private final IProductMappingService productMappingService;
    private final IProductService productService;
    private final ProductSupport productSupport;
    private final ITransactionRecordService iTransactionRecordService;
    private final IChannelWarehouseInfoServiceImpl channelWarehouseInfoService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final IStorageFeeInfoService iStorageFeeInfoService;
    private final ITransactionStorageFeeService transactionStorageFeeService;
    private final StorageFeeSupport storageFeeSupport;

    @Value("${distribution.airwallex.webhooksSecretKey}")
    public String airwallexSecretKey;

    /**
     * 调用接口次数
     */
    AtomicInteger callAirwallexNum = new AtomicInteger(0);

    /**
     * 查询租户渠道列表
     */
    @SaCheckPermission("distributor:salesChannel:list")
    @GetMapping("/list")
    public TableDataInfo<TenantSalesChannelVo> list(TenantSalesChannelBo bo, PageQuery pageQuery) {
        return iTenantSalesChannelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出租户渠道列表
     */
    @SaCheckPermission("distributor:salesChannel:export")
    @Log(title = "租户渠道", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R export(@RequestBody TenantSalesChannelBo bo, HttpServletResponse response) {
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.TENANT_SALES_CHANNEL_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        List<TenantSalesChannelVo> list = iTenantSalesChannelService.queryList(bo);
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.TenantSalesChannelExport, tempFileSavePath -> {
            List<TenantSalesChannelExportDTO> list1=new ArrayList<>();
            list.forEach(s -> {
                LambdaQueryWrapper<ChannelWarehouseInfo> o = new LambdaQueryWrapper<>();
                o.eq(ChannelWarehouseInfo::getTenantSaleChannelId, s.getId());
                List<ChannelWarehouseInfo> channelWarehouseInfoList = TenantHelper.ignore(() -> channelWarehouseInfoService.list(o));

                // 提取出来的设置DTO属性的方法
                Supplier<TenantSalesChannelExportDTO> createDtoSupplier = () -> {
                    TenantSalesChannelExportDTO te = new TenantSalesChannelExportDTO();
                    te.setChannelName(s.getChannelName());
                    te.setChannelAlias(s.getChannelAlias());
                    te.setChannelType(s.getChannelType());
                    te.setLogisticsType(getLogisticsTypeDesc(s.getLogisticsType()));
                    te.setState(s.getState() == 1 ? "启用" : "停用");
                    return te;
                };

                if (CollUtil.isEmpty(channelWarehouseInfoList)) {
                    list1.add(createDtoSupplier.get());
                } else {
                    channelWarehouseInfoList.forEach(ss -> {
                        TenantSalesChannelExportDTO te = createDtoSupplier.get();
                        te.setChannelWarehouseName(ss.getChannelWarehouseName());
                        te.setChannelWarehouseCode(ss.getChannelWarehouseCode());
                        te.setWarehouseCode(ss.getWarehouseCode());
                        list1.add(te);
                    });
                }
            });
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelUtil.exportExcelWithLocale(list1, "TenantSalesChannelExport", TenantSalesChannelExportDTO.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
     return R.ok();
    }

    private String getLogisticsTypeDesc(LogisticsTypeEnum logisticsType) {
        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
            return "自提";
        } else if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            return "代发";
        } else if (LogisticsTypeEnum.ZSMallDropShipping.equals(logisticsType)) {
            return "委托ZSMall代发";
        }
        return null; // 或者其他默认值
    }
    /**
     * 获取租户渠道详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("distributor:salesChannel:query")
    @GetMapping("/{id}")
    public R<TenantSalesChannelVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long id) {
        TenantSalesChannelVo tenantSalesChannelVo = iTenantSalesChannelService.queryByIdV2(id);
        LambdaQueryWrapper<ChannelWarehouseInfo> o = new LambdaQueryWrapper<>();
        o.eq(ChannelWarehouseInfo::getTenantSaleChannelId,tenantSalesChannelVo.getId());
        List<ChannelWarehouseInfo> list =   TenantHelper.ignore(()->channelWarehouseInfoService.list(o));
        tenantSalesChannelVo.setChannelWarehouseInfoList(list);
        return R.ok(tenantSalesChannelVo);
    }

    /**
     * 新增租户渠道
     */
    @SaCheckPermission("distributor:salesChannel:add")
    @Log(title = "租户渠道", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TenantSalesChannelBo bo) throws Exception {
        bo.setRequestMode(0);
        try {
            tenantSalesChannelService.validEntityBeforeSave(bo);
            ArrayList<TenantSalesChannel> tenantSalesChannels = iTenantSalesChannelService.insertByBoV2(bo);
            tenantSalesChannels.forEach(iTenantSalesChannelService::sendMqByTenantSalesChannel);
            return R.ok();
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 功能描述：测试添加
     *
     * @param bo bo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/06/11
     */
    @PostMapping("/testAdd")
    public R<Void> testAdd(@Validated(AddGroup.class) @RequestBody TenantSalesChannelBo bo) throws Exception {
//        tenantSalesChannelService.validEntityBeforeSave(bo);
        ArrayList<TenantSalesChannel> tenantSalesChannels = iTenantSalesChannelService.insertByBoV2(bo);
        return R.ok();
    }

    /**
     * 修改租户渠道
     */
    @Transactional(rollbackFor = Exception.class)
    @SaCheckPermission("distributor:salesChannel:edit")
    @Log(title = "租户渠道", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TenantSalesChannelBo bo) throws Exception {
        try {
            bo.setRequestMode(1);
            tenantSalesChannelService.validEntityBeforeSave(bo);
            //修改成功
            TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getById(bo.getId());
            if (ObjectUtil.isNull(tenantSalesChannel)){
                throw  new RuntimeException(StrUtil.format("渠道ID：{}不存在",bo.getId()));
            }
            if (!ObjectUtil.equals(bo.getLogisticsType(),tenantSalesChannel.getLogisticsType())){
                LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductMapping::getChannelId,tenantSalesChannel.getId());
                List<ProductMapping> productMappingList = productMappingService.getBaseMapper().selectList(wrapper);
                List<String> productSkuCodeNormal = new ArrayList<>();
                List<String> productSkuCodeException = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(productMappingList)){
                    productMappingList.forEach(s->{
                        //查询所属的Product发货方式
                        Product product = productService.queryByProductSkuCode(s.getProductSkuCode());
                        //判断渠道的新的发货方式和商品的方式是否一直
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(bo.getLogisticsType())));
                        //如果不一致，更新映射关系
                        UpdateWrapper<ProductMapping> up = new UpdateWrapper<>();
                        up.eq("id",s.getId());
                        up.set("update_time",new Date());
                        up.set("update_by",LoginHelper.getUserId());
                        if (!isSupport){
                            productSkuCodeException.add(s.getProductSkuCode());
                            up.set("sync_state", SyncStateEnum.Exceptions);
                        }else {
                            productSkuCodeNormal.add(s.getProductSkuCode());
                            up.set("sync_state", SyncStateEnum.Mapped);
                        }
                        TenantHelper.ignore(()->productMappingService.getBaseMapper().update(null,up));
                    });
                }
                //通知订单模块
                try {
                    if(CollUtil.isNotEmpty(productSkuCodeNormal)){
                        productSupport.orderExceptionDisposeRecover(productSkuCodeNormal, OrderExceptionEnum.product_mapping_exception);
                    }
                    if(CollUtil.isNotEmpty(productSkuCodeException)){
                        productSupport.orderExceptionDispose(productSkuCodeException,OrderExceptionEnum.product_mapping_exception);
                    }
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }
            TenantSalesChannel salesChannel = iTenantSalesChannelService.updateByBo(bo);
            iTenantSalesChannelService.sendMqByTenantSalesChannel(salesChannel);
            return R.ok("更新成功");
        }catch (Exception e){
            log.info(String.valueOf(e));
            return R.fail(e.getMessage());
        }
    }
    @PutMapping("/editTest")
    public R<Void> editTest(@Validated(EditGroup.class) @RequestBody TenantSalesChannelBo bo) throws Exception {
        tenantSalesChannelService.validEntityBeforeSave(bo);
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.updateByBo(bo);
        return R.ok("更新成功");
    }

    /**
     * 删除租户渠道
     *
     * @param ids 主键串
     */
    @SaCheckPermission("distributor:salesChannel:remove")
    @ApiImplicitParam(name = "ids", value = "租户主键", required = true)
    @Log(title = "租户渠道", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTenantSalesChannelService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 解绑租户渠道
     *
     * @param id 主键串
     */
    @SaCheckPermission("distributor:salesChannel:remove")
    @Log(title = "解绑租户渠道", businessType = BusinessType.DELETE)
    @ApiImplicitParam(name = "ids", value = "租户主键", required = true)
    @DeleteMapping("/unbind/{id}")
    public R<Void> unbinding(@NotNull(message = "主键不能为空") @PathVariable Long id) throws RStatusCodeException {
        return toAjax(tenantSalesChannelService.unbind(id));
    }


    /**
     * 绑定租户渠道
     *
     * @param channelBindingBo
     */
    @SaIgnore
    @SaCheckPermission("distributor:salesChannel:add")
    @Log(title = "绑定租户渠道", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/binding")
    public R<Void> bindChannel(TenantChannelBindingBo channelBindingBo) throws RStatusCodeException {
        return toAjax(tenantSalesChannelService.bindChannel(channelBindingBo));
    }


    /**
     * 跳转至指定渠道
     */
    @SaCheckPermission("distributor:salesChannel:add")
    @Log(title = "跳转至指定渠道", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/jumpToChannel")
    public R<String> jumpToChannel(@Validated(AddGroup.class) @RequestBody TenantSalesChannelBo bo) {
        String jumpUrl = tenantSalesChannelService.jumpToChannel(bo);
        return R.ok("success", jumpUrl);
    }

    /**
     * Wayfair沙盒调试
     *
     * @param channelSanboxBo
     * @return
     */
    @SaCheckPermission("distributor:salesChannel:add")
    @Log(title = "Wayfair沙盒调试", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/wayfairSandbox")
    public R<Void> wayfairSandbox(@Validated @RequestBody ChannelSanboxBo channelSanboxBo) throws RStatusCodeException {
        return toAjax(tenantSalesChannelService.wayfairSandbox(channelSanboxBo));
    }

    /**
     * 获取已关联的渠道分组
     */
    @GetMapping("/getEnableChannelGroup")
    public R<ChannelGroupListVo> getEnableChannelGroup(ChannelTypeBo bo) {
        return R.ok(tenantSalesChannelService.getEnableChannelGroup(bo));
    }

    /**
     * 根据字典获取关联的渠道分组
     *
     * @param bo
     * @return
     */
    @GetMapping("/getEnableChannelGroupByDict")
    public R<ChannelGroupListVo> getEnableChannelGroupByDict(ChannelTypeBo bo) {
        return R.ok(tenantSalesChannelService.getEnableChannelGroupByDict(bo));
    }

    /**
     * 获取授权 URL
     *
     * @param thirdChannelFlag    商店标志
     * @param channelType 通道类型
     * @return {@link R }<{@link String }>
     * <AUTHOR>
     * @date 2024/01/08
     */
    @GetMapping("/getAuthorizeUrl")
    public R<String> getAuthorizeUrl(
        @RequestParam(value = "thirdChannelFlag") String thirdChannelFlag,
        @RequestParam(value = "channelType") String channelType) {
        // 1. 校验店铺是否存在 2.拿店铺的 flag 拼接url
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(thirdChannelFlag, channelType);

        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("当前渠道下不存在该店铺"+"或者当前租户没有该店铺的操作权限");
        }
        String url = tenantSalesChannelService.getAuthorizeUrl(channelType, thirdChannelFlag);
        return R.ok("获取授权url成功",url);
    }

    /**
     * 功能描述：跳跃商店
     *
     * @param thirdChannelFlag 第三频道标志
     * @param channelType      通道类型
     * @return {@link R }<{@link String }>
     * <AUTHOR>
     * @date 2024/07/18
     */
    @GetMapping("/jumpStore")
    public R<String> jumpStore(
        @RequestParam(value = "thirdChannelFlag") String thirdChannelFlag,
        @RequestParam(value = "channelType") String channelType) {
        // 1. 校验店铺是否存在 2.拿店铺的 flag 拼接url
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(thirdChannelFlag, channelType);

        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("当前渠道下不存在该店铺"+"或者当前租户没有该店铺的操作权限");
        }
        String url = tenantSalesChannelService.skipStore(channelType, thirdChannelFlag);
        return R.ok("获取跳转url成功",url);
    }

    /**
     * 注册后-后端用
     *
     * @param thirdChannelFlag 商店标志
     * @return {@link R }<{@link String }>
     * <AUTHOR>
     * @date 2024/01/08
     */
    @GetMapping("/afterRegistration")
    public R<String> afterRegistration(String thirdChannelFlag) {
        /*  1. 授权商店成功,返回商店flag,修改渠道表为开通状态
         *  2. 将商店信息同步至 多渠道消息队列,确认消费+消息持久化
         */
        tenantSalesChannelService.sendTenantToMultiChannel(thirdChannelFlag, ChannelTypeEnum.Erp.getValue());

        return R.ok();
    }

    /**
     * 亚马逊供应商注册回调
     *
     * @param service  服务
     * @param request  请求
     * @param response 响应
     * <AUTHOR>
     * @date 2024/01/11
     */
    @RequestMapping("/amazonvendor/sp-api-oauth")
    @ResponseBody
    public void vendorCallaBack(@RequestParam(value = "service", required = false) String service, ServletRequest request, ServletResponse response) {
        // ?spapi_oauth_code=ANFkWpyQXkUTUnafECEG&state=examplestate&selling_partner_id=amzn1.vg.4044790
        Map<String, String[]> parameterMap = request.getParameterMap();

        //得到平台返回的code
        String sellerId = parameterMap.get("selling_partner_id")[0];
        String callBackCode = parameterMap.get("spapi_oauth_code")[0];
        String state = parameterMap.get("state")[0];

        log.info("parameterMap返回值为:{}", parameterMap);
        log.info("callBackCode返回值为:{}", callBackCode);

        //拿到code值再次请求
        String jumpUrl = iTenantSalesChannelService.acquireVendorRefreshToken(sellerId, callBackCode, state);

        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        httpServletResponse.setStatus(302);
        httpServletResponse.setHeader("Location", jumpUrl);
    }


    /**
     * 亚马逊店铺注册回调
     *
     * @param service  服务
     * @param request  请求
     * @param response 响应
     * <AUTHOR>
     * @date 2024/01/11
     */
    @RequestMapping("/amazon/sp-api-oauth")
    @ResponseBody
    public void amazonCallaBack(@RequestParam(value = "service", required = false) String service, ServletRequest request, ServletResponse response) {
        Map<String, String[]> parameterMap = request.getParameterMap();

        //得到平台返回的code
        String sellerId = parameterMap.get("selling_partner_id")[0];
        String callBackCode = parameterMap.get("spapi_oauth_code")[0];
        String state = parameterMap.get("state")[0];

        log.info("parameterMap返回值为:{}", parameterMap);
        log.info("callBackCode返回值为:{}", callBackCode);

        //拿到code值再次请求
        String jumpUrl = iTenantSalesChannelService.acquireAmazonRefreshToken(sellerId, callBackCode, state);

        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        httpServletResponse.setStatus(302);
        httpServletResponse.setHeader("Location", jumpUrl);
    }

    /**
     * 亚马逊店铺注册回调
     *
     * @param service  服务
     * @param request  请求
     * @param response 响应
     * <AUTHOR>
     * @date 2024/01/11
     */
    @RequestMapping("/amazon-sc/sp-api-oauth")
    @ResponseBody
    public void amazonScCallaBack(@RequestParam(value = "service", required = false) String service, ServletRequest request, ServletResponse response) {
        Map<String, String[]> parameterMap = request.getParameterMap();

        //得到平台返回的code
        String sellerId = parameterMap.get("selling_partner_id")[0];
        String callBackCode = parameterMap.get("spapi_oauth_code")[0];
        String state = parameterMap.get("state")[0];

        log.info("parameterMap返回值为:{}", parameterMap);
        log.info("callBackCode返回值为:{}", callBackCode);

        //拿到code值再次请求
        iTenantSalesChannelService.acquireAmazonScRefreshToken(sellerId, callBackCode, state);

    }

    /**
     * 亚马逊店铺注册回调
     *
     * @param service  服务
     * @param request  请求
     * @param response 响应
     * <AUTHOR>
     * @date 2024/01/11
     */
    @RequestMapping("/amazon-vc/sp-api-oauth")
    @ResponseBody
    public void amazonVcCallaBack(@RequestParam(value = "service", required = false) String service, ServletRequest request, ServletResponse response) {
        Map<String, String[]> parameterMap = request.getParameterMap();

        //得到平台返回的code
        String sellerId = parameterMap.get("selling_partner_id")[0];
        String callBackCode = parameterMap.get("spapi_oauth_code")[0];
        String state = parameterMap.get("state")[0];

        log.info("parameterMap返回值为:{}", parameterMap);
        log.info("callBackCode返回值为:{}", callBackCode);

        //拿到code值再次请求
        iTenantSalesChannelService.acquireAmazonVcRefreshToken(sellerId, callBackCode, state);

    }

    /**
     * TikTok 店铺注册回调
     *
     * @param request  请求
     * @param response
     * <AUTHOR>
     * @date 2024/01/11
     */
    @RequestMapping("/tiktok/authcallback")
    @ResponseBody
    public void tiktokAuthBack(ServletRequest request, ServletResponse response) {
        //拿到code值再次请求
        log.info("回调信息request:{},response{}",request.toString(),response.toString());
        String channelFlag = request.getParameter("state");
        String authorizeCode = request.getParameter("code");
        SaleChannelCallBackVo saleChannelCallBackVo = new SaleChannelCallBackVo();
        String jumpUrl = iTenantSalesChannelService.tiktokAuthorize(channelFlag, authorizeCode,saleChannelCallBackVo);
        if(ObjectUtil.isNotNull(saleChannelCallBackVo) && StringUtils.isNotEmpty(saleChannelCallBackVo.getConnectStr())){
            try {
                log.info("tiktok 店铺注册回调信息发送到多渠道,发送数据：{}",saleChannelCallBackVo);
                String str = JSON.toJSONString(saleChannelCallBackVo);
                String messageId = IdUtil.fastSimpleUUID();
                Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.TIKTOK_SHOP_ROUTING_KEY, message);
            }catch (Exception e){
                log.error("将回调信息发送到多渠道异常，异常信息: {}",e.getMessage());
            }
        }
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        httpServletResponse.setStatus(302);
        httpServletResponse.setHeader("Location", jumpUrl);
    }

    @GetMapping("/tiktok/authcallback/manual")
    public void tiktokAuthBack(String channelFlag,String code) {
        //拿到code值再次请求

        iTenantSalesChannelService.tiktokAuthorize(channelFlag, code,new SaleChannelCallBackVo());


    }

    /**
     * Airwallex Payment 回调接口
     *
     * @param request
     * @param payload
     * @return
     */
    @PostMapping("/airwallex/paymentCallback")
    public R airwallexPaymentReturnData(
        HttpServletRequest request, @RequestBody String payload
    ) throws Exception {
        callAirwallexNum.incrementAndGet();
        StringBuilder valueToDigest = new StringBuilder();
        String timestamp = request.getHeader("x-timestamp");
        log.info("airwallex param x-timestamp: {}",timestamp);
        valueToDigest.append(timestamp);
        valueToDigest.append(payload);
        String signature = request.getHeader("x-signature");
        log.info("airwallex param x-signature: {}",signature);
        if (HmacUtils.hmacSha256Hex(airwallexSecretKey, valueToDigest.toString()).equals(signature)) {
            log.info("验证通过");
        }else {
            log.info("验证失败");
            // 返回ok是因为airwallex回调接口若不返回ok会一直回调
            return R.ok();
        }
        JSONObject paymentIntentData = JSONObject.parseObject(payload);
        log.info("airwallex Payment 返回信息: {} 调用次数: {}", paymentIntentData,callAirwallexNum.get());
        if (null == paymentIntentData) {
            log.info("airwallex Payment 返回信息 为空!");
            return R.ok();
        }
        String requestId = paymentIntentData.getJSONObject("data").getJSONObject("object").getString("request_id");
        String status = paymentIntentData.getJSONObject("data").getJSONObject("object").getString("status");
        String currency = paymentIntentData.getJSONObject("data").getJSONObject("object").getString("base_currency");
        iAirwallexRequestInfoService.addAirwallexRequestLog(new AirwallexRequestLog(requestId,payload,LocalDateTime.now()));
        AirwallexRequestInfo airwallexRequestInfo = iAirwallexRequestInfoService.getByRequestId(requestId);
        try {
            if(StringUtils.isNotEmpty(status) && status.equals("SUCCEEDED")){
                if(null != airwallexRequestInfo && !new Integer(1).equals(airwallexRequestInfo.getIsSuccess())){
                    BigDecimal amount;
                    if(null != paymentIntentData.getJSONObject("data").getJSONObject("object").getBigDecimal("base_amount")){
                        amount = paymentIntentData.getJSONObject("data").getJSONObject("object").getBigDecimal("base_amount");
                    }else {
                        amount = paymentIntentData.getJSONObject("data").getJSONObject("object").getBigDecimal("amount");
                    }
                    String accountId = paymentIntentData.getString("account_id");
                    if(null != airwallexRequestInfo.getType()){
                        // 订单付款成功处理
                        if(airwallexRequestInfo.getType().equals(1)){
                            // 仓储费支付结果处理
                            if(null != airwallexRequestInfo.getPayType() && airwallexRequestInfo.getPayType().equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())){
                                if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())){
                                    List<StorageFeeInfo> storageFeeInfoList = iStorageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")))
                                                                                                                                                  .eq(StorageFeeInfo::getFeeState, FeeStateEnum.CONFIRMING.getValue()).in(StorageFeeInfo::getPayState, Arrays.asList(PayStateEnum.UNPAID.getValue(),PayStateEnum.FAILED.getValue())));
                                    if(CollUtil.isEmpty(storageFeeInfoList)){
                                        log.error("支付成功，airwallex仓储费支付，未获取到仓储费信息。仓储费ID {}",airwallexRequestInfo.getOrderNo());
                                    }else {
                                        storageFeeSupport.storageFeePayCallback(storageFeeInfoList,true,payload,airwallexRequestInfo.getPayType());
                                    }
                                }
                            }
                            // 需要排除仓储费支付的情况
                            if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())
                            && null != airwallexRequestInfo.getPayType() && !airwallexRequestInfo.getPayType().equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())){
                                List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")), OrderStateType.Pending);
                                if(CollectionUtil.isNotEmpty(ordersList)){
                                    orderSupport.orderPayForThird(airwallexRequestInfo.getTenantId(),ordersList,true,false);
                                    //更新订单支付方式
                                    ordersList.forEach(order -> {
                                        iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                                               .set(Orders::getPayType, OrderPayTypeEnum.Airwallex));
                                    });
                                }else {
                                    airwallexRequestInfo.setIsSuccess(2).setReturnInfo(payload).setExceptionMessage("没有订单信息"+ordersList);
                                    iAirwallexRequestInfoService.updateById(airwallexRequestInfo);
                                    log.error("/airwallex/paymentCallback 接口出现异常, 异常信息: {}","没有订单信息"+ordersList);
                                }
                            }
                        }
                        // 充值成功处理
                        if(airwallexRequestInfo.getType().equals(2)){
                            PaymentExtensionBo paymentExtensionBo = new PaymentExtensionBo();
                            String currencySymbol = iSiteCountryCurrencyService.getCurrencySymbolByCurrencyCode(currency);
                            paymentExtensionBo.setTenantId(airwallexRequestInfo.getTenantId()).setPaymentMethodType(TransactionMethodEnum.OnlineAirwallex.getValue()).setPaymentDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                                              .setTransactionType(TransactionTypeEnum.Recharge.getValue()).setNote("Airwallex pay").setAmount(amount).setAccountId(accountId).setCurrency(currency).setCurrencySymbol(currencySymbol);
                            paymentExtensionService.submitPaymentReceipt(paymentExtensionBo);
                        }
                    }
                    airwallexRequestInfo.setIsSuccess(1).setReturnInfo(payload);
                    iAirwallexRequestInfoService.updateById(airwallexRequestInfo);
                }
            }
            if(StringUtils.isNotEmpty(status) && status.equals("FAILED")){
                airwallexRequestInfo.setIsSuccess(2).setReturnInfo(payload).setExceptionMessage(payload);
                iAirwallexRequestInfoService.updateById(airwallexRequestInfo);
                // 订单失败处理
                if(airwallexRequestInfo.getType().equals(1)) {
                    if(null != airwallexRequestInfo.getPayType() && airwallexRequestInfo.getPayType().equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())){
                        if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())){
                            List<StorageFeeInfo> storageFeeInfoList = iStorageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")))
                                                                                                                                          .eq(StorageFeeInfo::getFeeState, FeeStateEnum.CONFIRMING.getValue()).in(StorageFeeInfo::getPayState, Arrays.asList(PayStateEnum.UNPAID.getValue(),PayStateEnum.FAILED.getValue())));
                            if(CollUtil.isEmpty(storageFeeInfoList)){
                                log.error("支付失败，airwallex仓储费支付，未获取到仓储费信息。仓储费ID {}",airwallexRequestInfo.getOrderNo());
                            }else {
                                storageFeeSupport.storageFeePayCallback(storageFeeInfoList,false,payload,airwallexRequestInfo.getPayType());
                            }
                        }
                    }
                    if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())
                    && null != airwallexRequestInfo.getPayType() && !airwallexRequestInfo.getPayType().equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())) {
                        List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo()
                                                                                                                                .split(",")), OrderStateType.Pending);
                        orderSupport.orderPayFailThird(ordersList, String.valueOf(paymentIntentData));
                    }
                }
            }
        }catch (Exception e){
            airwallexRequestInfo.setIsSuccess(2).setReturnInfo(payload).setExceptionMessage(e.getMessage());
            iAirwallexRequestInfoService.updateById(airwallexRequestInfo);
            log.error("/airwallex/paymentCallback 接口出现异常, 异常信息: {}",e.getMessage());
            // 订单失败处理
            if(airwallexRequestInfo.getType().equals(1)) {
                if(null != airwallexRequestInfo.getPayType() && airwallexRequestInfo.getPayType().equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())){
                    if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())){
                        List<StorageFeeInfo> storageFeeInfoList = iStorageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")))
                                                                                                                                      .eq(StorageFeeInfo::getFeeState, FeeStateEnum.CONFIRMING.getValue()).in(StorageFeeInfo::getPayState, Arrays.asList(PayStateEnum.UNPAID.getValue(),PayStateEnum.FAILED.getValue())));
                        if(CollUtil.isEmpty(storageFeeInfoList)){
                            log.error("支付失败，airwallex仓储费支付，未获取到仓储费信息。仓储费ID {}",airwallexRequestInfo.getOrderNo());
                        }else {
                            storageFeeSupport.storageFeePayCallback(storageFeeInfoList,false,payload,airwallexRequestInfo.getPayType());
                        }
                    }
                }
                if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo()) && null != airwallexRequestInfo.getPayType() && !airwallexRequestInfo.getPayType().equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())) {
                    List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo()
                                                                                                                            .split(",")), OrderStateType.Pending);
                    orderSupport.orderPayFailThird(ordersList, String.valueOf(paymentIntentData));
                }
            }
        }
        return R.ok();
    }

    /**
     * payoneerwebhooks通知接口
     *
     * @param payload
     * @return
     */
    @PostMapping("/payoneer/notifications")
    public R payoneerNotifications(
        HttpServletRequest request, @RequestBody String payload
    ) {
        log.info("payonner通知信息: {}",payload);
        String authorization = request.getHeader("Authorization");
        log.info("payonner authorization: {}",authorization);
        if(StringUtils.isEmpty(payload)){
            log.info("payonner通知信息为空!");
            return R.ok();
        }
        JSONObject notificationData = JSONObject.parseObject(payload);
        if (null == notificationData) {
            log.info("payonner通知信息转化为JSONObject为空!");
            return R.ok();
        }
        String requestId = notificationData.getString("transactionId");
        String interactionReason = notificationData.getString("interactionReason");
        String interactionCode = notificationData.getString("interactionCode");
        String reasonCode = notificationData.getString("reasonCode");
        String statusCode = notificationData.getString("statusCode");
        iAirwallexRequestInfoService.addAirwallexRequestLog(new AirwallexRequestLog(requestId,payload,LocalDateTime.now()));
        AirwallexRequestInfo airwallexRequestInfo = iAirwallexRequestInfoService.getByRequestId(requestId);
        try {
            if(StringUtils.isNotEmpty(interactionCode) && interactionCode.equals("PROCEED")
                && StringUtils.isNotEmpty(reasonCode) && reasonCode.equals("debited")
                && StringUtils.isNotEmpty(statusCode) && statusCode.equals("charged")
                && StringUtils.isNotEmpty(interactionReason) && interactionReason.equals("OK")){
                    if(null != airwallexRequestInfo && !new Integer(1).equals(airwallexRequestInfo.getIsSuccess())){
                        if(null != airwallexRequestInfo.getType()){
                            // 订单付款成功处理
                            if(airwallexRequestInfo.getType().equals(1) && null != airwallexRequestInfo.getAesKey()){
                                if(StringUtils.isNotEmpty(authorization)){
                                    String transactionId = decrypt(authorization, airwallexRequestInfo.getAesKey());
                                    if(!requestId.equals(transactionId)){
                                        log.error("派安盈webhooks调用校验失败!");
                                        return R.ok();
                                    }
                                }else {
                                    log.error("派安盈webhooks请求头没有authorization信息!");
                                    return R.ok();
                                }
                                if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())){
                                    // 仓储费支付结果处理
                                    if(null != airwallexRequestInfo.getPayType() && airwallexRequestInfo.getPayType().equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())){
                                        if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())){
                                            List<StorageFeeInfo> storageFeeInfoList = iStorageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")))
                                                                                                                                                          .eq(StorageFeeInfo::getFeeState, FeeStateEnum.CONFIRMING.getValue()).in(StorageFeeInfo::getPayState, Arrays.asList(PayStateEnum.UNPAID.getValue(),PayStateEnum.FAILED.getValue())));
                                            if(CollUtil.isEmpty(storageFeeInfoList)){
                                                log.error("支付成功，payoneer仓储费支付，未获取到仓储费信息。仓储费ID {}",airwallexRequestInfo.getOrderNo());
                                            }else {
                                                storageFeeSupport.storageFeePayCallback(storageFeeInfoList,true,payload,airwallexRequestInfo.getPayType());
                                            }
                                        }
                                    }
                                    if(null != airwallexRequestInfo.getPayType() && !airwallexRequestInfo.getPayType().equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())){
                                        List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")), OrderStateType.Pending);
                                        if(CollectionUtil.isNotEmpty(ordersList)){
                                            orderSupport.orderPayForThird(airwallexRequestInfo.getTenantId(),ordersList,true,false);
                                            //更新订单支付方式
                                            ordersList.forEach(order -> {
                                                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                                                       .set(Orders::getPayType, OrderPayTypeEnum.Payoneer));
                                            });
                                        }else {
                                            airwallexRequestInfo.setIsSuccess(2).setReturnInfo(payload).setExceptionMessage("没有订单信息"+ordersList);
                                            iAirwallexRequestInfoService.updateById(airwallexRequestInfo);
                                            log.error("/payoneer/notifications 接口出现异常, 异常信息: {}","没有订单信息"+ordersList);
                                        }
                                    }
                                }
                            }
                        }
                        airwallexRequestInfo.setIsSuccess(1).setReturnInfo(payload);
                        iAirwallexRequestInfoService.updateById(airwallexRequestInfo);
                    }
                }
            // 订单支付失败处理
            if(StringUtils.isNotEmpty(interactionReason) && !interactionReason.equals("OK") && !interactionReason.equals("PENDING")  && !interactionReason.equals("CUSTOMER_ABORT")){
                if(null != airwallexRequestInfo && !new Integer(1).equals(airwallexRequestInfo.getIsSuccess()) && StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())) {
                    // 仓储费支付结果处理
                    if(null != airwallexRequestInfo.getPayType() && airwallexRequestInfo.getPayType().equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())){
                        List<StorageFeeInfo> storageFeeInfoList = iStorageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")))
                                                                                                                                      .eq(StorageFeeInfo::getFeeState, FeeStateEnum.CONFIRMING.getValue()).in(StorageFeeInfo::getPayState, Arrays.asList(PayStateEnum.UNPAID.getValue(),PayStateEnum.FAILED.getValue())));
                        if(CollUtil.isEmpty(storageFeeInfoList)){
                            log.error("支付失败，payoneer仓储费支付，未获取到仓储费信息。仓储费ID {}",airwallexRequestInfo.getOrderNo());
                        }else {
                            storageFeeSupport.storageFeePayCallback(storageFeeInfoList,false,payload,airwallexRequestInfo.getPayType());
                        }
                    }
                    if(null != airwallexRequestInfo.getPayType() && !airwallexRequestInfo.getPayType().equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())) {
                        // 订单失败处理
                        List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo()
                                                                                                                                .split(",")), OrderStateType.Pending);
                        orderSupport.orderPayFailThird(ordersList, payload);
                    }
                }
            }
            // 订单未支付处理-中止支付的情况
            if(StringUtils.isNotEmpty(interactionReason) && interactionReason.equals("CUSTOMER_ABORT")
                && StringUtils.isNotEmpty(interactionCode) && interactionCode.equals("ABORT")
                && StringUtils.isNotEmpty(reasonCode) && reasonCode.equals("list_canceled")
                && StringUtils.isNotEmpty(statusCode) && statusCode.equals("canceled")
            ){
                if(null != airwallexRequestInfo && !new Integer(1).equals(airwallexRequestInfo.getIsSuccess()) && StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())) {
                    List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")), OrderStateType.Pending);
                    if(CollectionUtil.isNotEmpty(ordersList)){
                        // 修改订单的状态为未支付
                        ordersList.forEach(order -> {
                            iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                                   .set(Orders::getOrderState, OrderStateType.UnPaid));
                        });
                    }
                }
            }
            // 订单未支付处理-支付时间过期的情况
            if(StringUtils.isNotEmpty(interactionReason) && interactionReason.equals("OK")
                && StringUtils.isNotEmpty(interactionCode) && interactionCode.equals("PROCEED")
                && StringUtils.isNotEmpty(reasonCode) && reasonCode.equals("list_expired")
                && StringUtils.isNotEmpty(statusCode) && statusCode.equals("expired")
            ){
                if(null != airwallexRequestInfo && !new Integer(1).equals(airwallexRequestInfo.getIsSuccess()) && StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())) {
                    List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")), OrderStateType.Pending);
                    if(CollectionUtil.isNotEmpty(ordersList)){
                        // 修改订单的状态为未支付
                        ordersList.forEach(order -> {
                            iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                                   .set(Orders::getOrderState, OrderStateType.UnPaid));
                        });
                    }
                }
            }
        }catch (Exception e){
            airwallexRequestInfo.setIsSuccess(2).setReturnInfo(payload).setExceptionMessage(e.getMessage());
            iAirwallexRequestInfoService.updateById(airwallexRequestInfo);
            log.error("/airwallex/paymentCallback 接口出现异常, 异常信息: {}",e.getMessage());
            if(StringUtils.isNotEmpty(airwallexRequestInfo.getOrderNo())) {
                // 仓储费支付结果处理
                if(null != airwallexRequestInfo.getPayType() && airwallexRequestInfo.getPayType().equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())){
                    List<StorageFeeInfo> storageFeeInfoList = iStorageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().in(StorageFeeInfo::getStorageFeeId, Arrays.asList(airwallexRequestInfo.getOrderNo().split(",")))
                                                                                                                                  .eq(StorageFeeInfo::getFeeState, FeeStateEnum.CONFIRMING.getValue()).in(StorageFeeInfo::getPayState, Arrays.asList(PayStateEnum.UNPAID.getValue(),PayStateEnum.FAILED.getValue())));
                    if(CollUtil.isEmpty(storageFeeInfoList)){
                        log.error("支付失败，payoneer仓储费支付，未获取到仓储费信息。仓储费ID {}",airwallexRequestInfo.getOrderNo());
                    }else {
                        storageFeeSupport.storageFeePayCallback(storageFeeInfoList,false,payload,airwallexRequestInfo.getPayType());
                    }
                }
                if(null != airwallexRequestInfo.getPayType() && !airwallexRequestInfo.getPayType().equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())) {
                    // 订单失败处理
                    List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(Arrays.asList(airwallexRequestInfo.getOrderNo()
                                                                                                                            .split(",")), OrderStateType.Pending);
                    orderSupport.orderPayFailThird(ordersList, payload);
                }
            }
        }
        return R.ok();
    }

    /**
     * 派安盈 Charge Account/Card webhooks
     *
     * @param request
     * @param payload
     * @return
     */
    @PostMapping("/payoneer/chargeAccountOrCard/notifications")
    public R payoneerChargeAccountOrCardNotifications(
        HttpServletRequest request, @RequestBody String payload
    ) {
        log.info("payonner Account/Card webhooks 通知信息: {}",payload);
        if(StringUtils.isNotEmpty(payload)){
            try {
                JSONObject payonnerResult = JSONObject.parseObject(payload);
                if(null != payonnerResult) {
                    String intPaymentId = payonnerResult.getString("IntPaymentId");
                    if (null != intPaymentId) {
                        TransactionRecord transactionRecord = iTransactionRecordService.findByTransactionNoAndTransactionStateEnumWithNotTenant(intPaymentId, TransactionStateEnum.Processing);
                        if(null != transactionRecord && null != transactionRecord.getTransactionAmount()){
                            BigDecimal amount = payonnerResult.getBigDecimal("Amount");
                            if(null != amount && amount.equals(transactionRecord.getTransactionAmount())){
//                                transactionRecord.setTransactionState(TransactionStateEnum.Success);
//                                iTransactionRecordService.updateById(transactionRecord);
                                log.info("payoneer充值接收到回调");
                            }
                        }
                    }
                }
            }catch (Exception e){
                log.error("派安盈 Charge Account/Card webhooks,出现异常: {}",e.getMessage());
                return R.ok();
            }
        }
        return R.ok();
    }

    /**
     * 连连支付收单 webhooks
     *
     * @param request
     * @param payload
     * @return
     */
    @PostMapping("/lianlian/checkout/notifications")
    public R lianLianPayNotifications(
        HttpServletRequest request, @RequestBody String payload
    ) throws JsonProcessingException {

        log.info("连连支付收单 通知信息: {}",payload);

        String requestHeaders = convertRequestHeadersToJson(request);

        log.info("请求头信息: {}", requestHeaders);

        return R.ok("success");
    }

    public String convertRequestHeadersToJson(HttpServletRequest request) throws JsonProcessingException {
        // 创建一个Map来存储请求头信息
        Map<String, String> headersMap = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();

        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headersMap.put(headerName, headerValue);
        }

        // 使用Jackson库将Map转换成JSON字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonHeaders = objectMapper.writeValueAsString(headersMap);

        return jsonHeaders;
    }

    /**
     * 获取店铺名称层级关系
     */
    @GetMapping("/getTenantSalesChannelHierarchy")
    @ResponseBody
    public R getTenantSalesChannelHierarchy(@RequestParam(value = "tenantType", required = false) String tenantType) {
        return  iTenantSalesChannelService.getTenantSalesChannelHierarchy(tenantType);
    }

    @PostMapping("test")
    public void test(String message){
            rabbitTemplate.convertAndSend(RabbitMqConstant.TEMU_ORDER_INFO_EXCHANGE,RabbitMqConstant.TEMU_ORDER_INFO_ROUTING_KEY,message);
        }



}
